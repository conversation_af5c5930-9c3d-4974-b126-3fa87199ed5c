import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export function middleware(request: NextRequest) {
  const response = NextResponse.next();

  // Apply cache prevention headers to sensitive routes
  const sensitiveRoutes = ["/form", "/application", "/thank-you", "/login"];
  const isApiRoute = request.nextUrl.pathname.startsWith("/api");
  const isSensitiveRoute = sensitiveRoutes.some((route) =>
    request.nextUrl.pathname.startsWith(route)
  );

  if (isApiRoute || isSensitiveRoute) {
    // Comprehensive cache prevention headers
    response.headers.set(
      "Cache-Control",
      "no-cache, no-store, must-revalidate, private"
    );
    response.headers.set("Pragma", "no-cache");
    response.headers.set("Expires", "0");

    // Additional security headers for sensitive content
    response.headers.set("X-Content-Type-Options", "nosniff");
    response.headers.set("X-Frame-Options", "Allow-From origin");
    response.headers.set("X-XSS-Protection", "1; mode=block");
    response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");

    // Prevent content from being stored in browser history
    response.headers.set("Vary", "Authorization, Accept-Encoding");
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};
