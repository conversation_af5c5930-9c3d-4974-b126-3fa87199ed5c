/** @type {import('next').NextConfig} */
const { version } = require("./package.json");

const nextConfig = {
  poweredByHeader: false, // Hide x-powered-by header
  images: {
    domains: [process.env.NEXT_PUBLIC_S3_DOMAIN],
    // Add image optimization settings to prevent preload issues
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  env: {
    VERSION: version,
  },
  // Optimize CSS loading to reduce preload warnings
  experimental: {
    optimizeCss: false, // Disable to prevent ReactDOM.preload empty href errors
  },
  async headers() {
    return [
      {
        // Apply security headers to all routes
        source: "/(.*)",
        headers: [
          // Content Security Policy - Environment-aware CSP
          {
            key: "Content-Security-Policy",
            value: (() => {
              // Properly detect environment
              const isDev = process.env.NEXT_PUBLIC_NODE_ENV === "dev";

              return [
                "default-src 'self'",
                `script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: https://www.googletagmanager.com https://www.google-analytics.com https://cmp.osano.com https://*.osano.com https://www.gstatic.com https://www.google.com https://www.recaptcha.net${
                  isDev ? " *" : " *"
                }`,
                // More restrictive worker-src
                `worker-src 'self' blob:${isDev ? " *" : " *"}`,
                `style-src 'self' 'unsafe-inline' https://fonts.googleapis.com${
                  isDev ? " *" : " *"
                }`,
                "font-src 'self' https://fonts.gstatic.com data:",
                `img-src 'self' data: blob: https: http:${isDev ? " *" : ""}`,
                `connect-src 'self' https://www.google-analytics.com https://region1.google-analytics.com https://analytics.google.com https://stats.g.doubleclick.net https://www.googletagmanager.com https://www.google.com https://google.com https://ipapi.co https://cmp.osano.com https://*.osano.com https://*.amazonaws.com https://*.amplifyapp.com https://*.apphero.io${
                  isDev ? " *" : " *"
                }`,
                "frame-src 'self' https://www.google.com https://www.recaptcha.net https://recaptcha.google.com https://www.googletagmanager.com https://td.doubleclick.net https://*.doubleclick.net https://*.apphero.io https://*.amplifyapp.com",
                "object-src 'none'",
                "base-uri 'self'",
                "form-action 'self'",
                "frame-ancestors 'self'",
                "upgrade-insecure-requests",
              ].join("; ");
            })(),
          },
          // X-Content-Type-Options
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          // X-Frame-Options
          {
            key: "X-Frame-Options",
            value: "ALLOW-FROM origin",
          },
          // Referrer Policy
          {
            key: "Referrer-Policy",
            value: "strict-origin-when-cross-origin",
          },
          // X-XSS-Protection
          {
            key: "X-XSS-Protection",
            value: "1; mode=block",
          },
          // Strict Transport Security
          {
            key: "Strict-Transport-Security",
            value: "max-age=31536000; includeSubDomains; preload",
          },
          // Permissions Policy
          {
            key: "Permissions-Policy",
            value: ["geolocation=()"].join(", "),
          },
          // Cross-Origin-Embedder-Policy
          {
            key: "Cross-Origin-Embedder-Policy",
            value: "unsafe-none",
          },
          // Cross-Origin-Opener-Policy
          {
            key: "Cross-Origin-Opener-Policy",
            value: "same-origin-allow-popups",
          },
          // Cross-Origin-Resource-Policy
          {
            key: "Cross-Origin-Resource-Policy",
            value: "cross-origin",
          },
          // Require SRI for subresources
          {
            key: "Require-SRI-For",
            value: "script style",
          },
          // Cache Control - Prevent caching of sensitive content
          {
            key: "Cache-Control",
            value: "no-cache, no-store, must-revalidate, private",
          },
          // Pragma - HTTP/1.0 compatibility
          {
            key: "Pragma",
            value: "no-cache",
          },
          // Expires - Set expiration date in the past
          {
            key: "Expires",
            value: "0",
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
